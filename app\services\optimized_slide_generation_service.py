"""
Optimized Slide Generation Service
Workflow tối ưu hóa quy trình tạo slide từ nội dung bài học theo 3 bước:
1. <PERSON><PERSON><PERSON> dựng khung slide
2. <PERSON> tiết hóa từng slide  
3. Gắn placeholder
"""

import logging
import threading
import re
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.services.llm_service import get_llm_service
from app.services.textbook_retrieval_service import TextbookRetrievalService
from app.services.google_slides_service import get_google_slides_service

logger = logging.getLogger(__name__)


class OptimizedSlideGenerationService:
    """
    Service tối ưu hóa để sinh nội dung slide theo workflow 3 bước
    Singleton pattern với Lazy Initialization
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """Singleton pattern implementation với thread-safe"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(OptimizedSlideGenerationService, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Lazy initialization - chỉ khởi tạo một lần"""
        if self._initialized:
            return

        self.llm_service = None
        self.textbook_service = None
        self.slides_service = None
        self._service_initialized = False
        self._initialized = True

    def _ensure_service_initialized(self):
        """Ensure services are initialized"""
        if not self._service_initialized:
            logger.info("🔄 OptimizedSlideGenerationService: First-time initialization triggered")
            self.llm_service = get_llm_service()
            self.textbook_service = TextbookRetrievalService()
            self.slides_service = get_google_slides_service()
            self._service_initialized = True
            logger.info("✅ OptimizedSlideGenerationService: Initialization completed")

    def is_available(self) -> bool:
        """Kiểm tra service có sẵn sàng không"""
        self._ensure_service_initialized()
        return (self.llm_service and self.llm_service.is_available() and 
                self.slides_service and self.slides_service.is_available())

    async def generate_slides_from_lesson_optimized(
        self,
        lesson_id: str,
        template_id: str,
        config_prompt: Optional[str] = None,
        presentation_title: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Tạo slides từ lesson_id và template_id theo WORKFLOW TỐI ƯU HÓA 3 BƯỚC

        Args:
            lesson_id: ID của bài học
            template_id: ID của Google Slides template
            config_prompt: Prompt cấu hình tùy chỉnh (optional)
            presentation_title: Tiêu đề presentation tùy chỉnh (optional)

        Returns:
            Dict chứa kết quả tạo slides
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Optimized slide generation service not available"
            }

        try:
            logger.info(f"🚀 Starting OPTIMIZED 3-STEP slide generation workflow for lesson {lesson_id}")

            # Validation: Kiểm tra input
            if not lesson_id or not lesson_id.strip():
                return {
                    "success": False,
                    "error": "Empty or invalid lesson_id provided"
                }
            
            if not template_id or not template_id.strip():
                return {
                    "success": False,
                    "error": "Empty or invalid template_id provided"
                }

            # Lấy nội dung bài học
            lesson_result = await self.textbook_service.get_lesson_content(lesson_id)
            if not lesson_result:
                return {
                    "success": False,
                    "error": f"Could not retrieve lesson content for {lesson_id}"
                }

            lesson_content = lesson_result.get("lesson_content", "")
            if not lesson_content or not lesson_content.strip():
                return {
                    "success": False,
                    "error": f"Empty lesson content for {lesson_id}"
                }

            logger.info(f"📚 Retrieved lesson content: {len(lesson_content)} characters")

            # Copy template và phân tích cấu trúc
            new_title = presentation_title or f"Bài học {lesson_id} - {datetime.now().strftime('%Y%m%d_%H%M%S')}"
            copy_and_analyze_result = await self.slides_service.copy_and_analyze_template(template_id, new_title)
            if not copy_and_analyze_result["success"]:
                return {
                    "success": False,
                    "error": f"Could not copy and analyze template: {copy_and_analyze_result['error']}"
                }

            logger.info(f"📋 Template copied and analyzed successfully")

            # WORKFLOW TỐI ƯU HÓA 3 BƯỚC
            workflow_result = await self._execute_optimized_workflow(
                lesson_content,
                copy_and_analyze_result,
                config_prompt
            )
            
            if not workflow_result["success"]:
                return workflow_result

            logger.info("🎉 Optimized 3-step workflow completed successfully!")
            
            return {
                "success": True,
                "lesson_id": lesson_id,
                "original_template_id": template_id,
                "presentation_id": copy_and_analyze_result["copied_presentation_id"],
                "presentation_title": copy_and_analyze_result["presentation_title"],
                "web_view_link": copy_and_analyze_result["web_view_link"],
                "workflow_result": workflow_result,
                "template_info": {
                    "title": copy_and_analyze_result["presentation_title"],
                    "layouts_count": copy_and_analyze_result["slide_count"]
                }
            }

        except Exception as e:
            logger.error(f"Error in optimized slide generation: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_optimized_workflow(
        self,
        lesson_content: str,
        copied_presentation_info: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Thực thi workflow tối ưu hóa 3 bước:
        1. Xây dựng khung slide
        2. Chi tiết hóa từng slide
        3. Gắn placeholder
        """
        try:
            logger.info("🔄 Executing optimized 3-step workflow...")

            # BƯỚC 1: Xây dựng khung slide
            logger.info("📋 STEP 1: Building slide framework...")
            slide_framework = await self._step1_build_slide_framework(
                lesson_content, 
                config_prompt
            )
            if not slide_framework["success"]:
                return slide_framework

            logger.info(f"✅ Step 1 completed: {len(slide_framework['slides'])} slides in framework")

            # BƯỚC 2: Chi tiết hóa từng slide (tuần tự)
            logger.info("📝 STEP 2: Detailing each slide sequentially...")
            detailed_slides = await self._step2_detail_slides_sequentially(
                lesson_content,
                slide_framework["slides"],
                config_prompt
            )
            if not detailed_slides["success"]:
                return detailed_slides

            logger.info(f"✅ Step 2 completed: {len(detailed_slides['slides'])} slides detailed")

            # BƯỚC 3: Gắn placeholder (riêng biệt)
            logger.info("🏷️ STEP 3: Attaching placeholders...")
            final_slides = await self._step3_attach_placeholders(
                detailed_slides["slides"]
            )
            if not final_slides["success"]:
                return final_slides

            logger.info(f"✅ Step 3 completed: {len(final_slides['slides'])} slides with placeholders")

            return {
                "success": True,
                "slides": final_slides["slides"],
                "framework": slide_framework,
                "detailed": detailed_slides,
                "final": final_slides
            }

        except Exception as e:
            logger.error(f"Error in optimized workflow execution: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    # Các default_prompt sẽ được định nghĩa ở đây
    def _get_default_prompt_1(self) -> str:
        """Default prompt cho Bước 1: Xây dựng khung slide"""
        return """
Bạn là chuyên gia phân tích nội dung giáo dục. Nhiệm vụ của bạn là phân tích nội dung bài học và tạo ra KHUNG SLIDE tổng quát.

NGUYÊN TẮC THIẾT KẾ KHUNG:
1. PHÂN TÍCH TOÀN DIỆN - Hiểu rõ nội dung bài học và xác định các chủ đề chính
2. CẤU TRÚC LOGIC - Sắp xếp các slide theo thứ tự logic từ tổng quan đến chi tiết
3. MỤC ĐÍCH RÕ RÀNG - Mỗi slide có mục đích và ý định truyền đạt cụ thể
4. TÍNH LIÊN KẾT - Các slide có mối liên hệ và dẫn dắt tự nhiên

YÊU CẦU OUTPUT:
- Tạo khung slide với tiêu đề và mục đích của từng slide
- Không cần nội dung chi tiết, chỉ cần khung tổng quát
- Đảm bảo tính logic và dễ theo dõi
- Số lượng slide phù hợp với nội dung (thường 6-10 slides)
"""

    def _get_default_prompt_2(self) -> str:
        """Default prompt cho Bước 2: Chi tiết hóa nội dung"""
        return """
Bạn là chuyên gia viết nội dung thuyết trình giáo dục. Nhiệm vụ của bạn là chi tiết hóa nội dung cho một slide cụ thể.

NGUYÊN TẮC CHI TIẾT HÓA:
1. NỘI DUNG ĐẦY ĐỦ - Cung cấp thông tin chi tiết và đầy đủ cho slide
2. PHONG CÁCH PHÙ HỢP - Điều chỉnh cách nói phù hợp với đối tượng và bối cảnh
3. TÍNH CHÍNH XÁC - Đảm bảo thông tin chính xác và có căn cứ
4. DỄ HIỂU - Trình bày rõ ràng, dễ hiểu cho người nghe

YÊU CẦU OUTPUT:
- Tạo nội dung chi tiết cho slide được chỉ định
- Nội dung phù hợp với mục đích của slide
- Sử dụng ngôn ngữ phù hợp với đối tượng
- Đảm bảo tính chính xác và đầy đủ
"""

    def _get_default_prompt_3(self) -> str:
        """Default prompt cho Bước 3: Gắn placeholder"""
        return """
Bạn là chuyên gia xử lý định dạng nội dung slide. Nhiệm vụ của bạn là gắn placeholder cho nội dung slide đã chi tiết hóa.

QUY TẮC GẮN PLACEHOLDER:
1. CHÍNH XÁC - Gắn đúng loại placeholder cho từng loại nội dung
2. NHẤT QUÁN - Sử dụng format placeholder thống nhất
3. ĐẦY ĐỦ - Không bỏ sót bất kỳ nội dung nào cần gắn placeholder
4. TUÂN THỦ - Theo đúng quy tắc placeholder hiện tại

PLACEHOLDER TYPES:
- LessonName, LessonDescription, CreatedDate
- TitleName, TitleContent
- SubtitleName, SubtitleContent
- ImageName, ImageContent

YÊU CẦU OUTPUT:
- Gắn placeholder cho tất cả nội dung trong slide
- Sử dụng format: nội dung #*(PlaceholderType)*#
- Đảm bảo không thay đổi ý nghĩa nội dung gốc
- Tuân thủ quy tắc gắn placeholder hiện tại
"""

    async def _step1_build_slide_framework(
        self,
        lesson_content: str,
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        BƯỚC 1: Xây dựng khung slide tổng quát

        Args:
            lesson_content: Nội dung bài học
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            Dict chứa khung slide đã tạo
        """
        try:
            logger.info("📋 Step 1: Building slide framework from lesson content...")

            # Validation input
            if not lesson_content or not lesson_content.strip():
                return {
                    "success": False,
                    "error": "Empty lesson content provided for framework building"
                }

            # Tạo prompt cho bước 1
            default_prompt_1 = self._get_default_prompt_1()
            final_config = config_prompt if config_prompt else ""

            framework_prompt = f"""
{default_prompt_1}

{final_config}

NỘI DUNG BÀI HỌC:
{lesson_content}

HƯỚNG DẪN TẠO KHUNG SLIDE:
1. Phân tích nội dung bài học và xác định các chủ đề chính
2. Tạo khung slide với cấu trúc logic từ tổng quan đến chi tiết
3. Mỗi slide có tiêu đề rõ ràng và mục đích cụ thể
4. Đảm bảo tính liên kết giữa các slide

FORMAT OUTPUT:
SLIDE 1: [Tiêu đề slide]
Mục đích: [Mục đích và ý định truyền đạt]
Kiến thức cần truyền đạt: [Kiến thức chính]

SLIDE 2: [Tiêu đề slide]
Mục đích: [Mục đích và ý định truyền đạt]
Kiến thức cần truyền đạt: [Kiến thức chính]

... (tiếp tục cho các slide khác)

Hãy tạo khung slide hoàn chỉnh và logic cho nội dung bài học trên.
"""

            # Gọi LLM để tạo khung slide
            logger.info("🤖 Calling LLM to generate slide framework...")
            llm_result = await self.llm_service.generate_content(
                prompt=framework_prompt,
                temperature=0.1,
                max_tokens=10000
            )

            if not llm_result["success"] or not llm_result.get("text"):
                return {
                    "success": False,
                    "error": f"Failed to generate slide framework: {llm_result.get('error', 'Empty response')}"
                }

            # Parse framework response
            framework_text = llm_result["text"].strip()
            parsed_framework = self._parse_slide_framework(framework_text)

            if not parsed_framework:
                return {
                    "success": False,
                    "error": "Failed to parse slide framework from LLM response"
                }

            logger.info(f"✅ Step 1 completed: Generated framework with {len(parsed_framework)} slides")

            return {
                "success": True,
                "slides": parsed_framework,
                "raw_framework": framework_text
            }

        except Exception as e:
            logger.error(f"Error in step 1 - building slide framework: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _parse_slide_framework(self, framework_text: str) -> List[Dict[str, Any]]:
        """
        Parse framework text từ LLM response thành structured data

        Args:
            framework_text: Text response từ LLM

        Returns:
            List các slide framework đã parse
        """
        try:
            slides = []
            lines = framework_text.split('\n')
            current_slide = None

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Detect slide header: "SLIDE X: [Title]"
                if line.startswith('SLIDE ') and ':' in line:
                    if current_slide:
                        slides.append(current_slide)

                    # Extract slide number and title
                    parts = line.split(':', 1)
                    slide_header = parts[0].strip()  # "SLIDE X"
                    slide_title = parts[1].strip() if len(parts) > 1 else ""

                    # Extract slide number
                    slide_num_match = re.search(r'SLIDE (\d+)', slide_header)
                    slide_number = int(slide_num_match.group(1)) if slide_num_match else len(slides) + 1

                    current_slide = {
                        "slide_number": slide_number,
                        "title": slide_title,
                        "purpose": "",
                        "knowledge": "",
                        "raw_content": []
                    }

                elif current_slide:
                    # Parse purpose and knowledge
                    if line.startswith('Mục đích:'):
                        current_slide["purpose"] = line.replace('Mục đích:', '').strip()
                    elif line.startswith('Kiến thức cần truyền đạt:'):
                        current_slide["knowledge"] = line.replace('Kiến thức cần truyền đạt:', '').strip()
                    else:
                        current_slide["raw_content"].append(line)

            # Add last slide
            if current_slide:
                slides.append(current_slide)

            logger.info(f"📋 Parsed {len(slides)} slides from framework")
            return slides

        except Exception as e:
            logger.error(f"Error parsing slide framework: {e}")
            return []

    async def _step2_detail_slides_sequentially(
        self,
        lesson_content: str,
        slide_framework: List[Dict[str, Any]],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        BƯỚC 2: Chi tiết hóa từng slide tuần tự

        Args:
            lesson_content: Nội dung bài học gốc
            slide_framework: Khung slide từ bước 1
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            Dict chứa các slide đã chi tiết hóa
        """
        try:
            logger.info(f"📝 Step 2: Detailing {len(slide_framework)} slides sequentially...")

            if not slide_framework:
                return {
                    "success": False,
                    "error": "Empty slide framework provided for detailing"
                }

            detailed_slides = []

            # Xử lý tuần tự từng slide: Slide 1 → Slide 2 → Slide 3 → ...
            for i, slide_info in enumerate(slide_framework):
                slide_number = slide_info.get("slide_number", i + 1)
                slide_title = slide_info.get("title", "")
                slide_purpose = slide_info.get("purpose", "")
                slide_knowledge = slide_info.get("knowledge", "")

                logger.info(f"🔄 Processing slide {slide_number}: {slide_title}")

                # Chi tiết hóa slide này
                detailed_result = await self._detail_single_slide(
                    lesson_content,
                    slide_info,
                    config_prompt
                )

                if detailed_result["success"]:
                    detailed_slides.append(detailed_result["slide"])
                    logger.info(f"✅ Slide {slide_number} detailed successfully")
                else:
                    logger.warning(f"❌ Failed to detail slide {slide_number}: {detailed_result.get('error')}")
                    # Tiếp tục với slide tiếp theo thay vì dừng toàn bộ workflow
                    continue

            if not detailed_slides:
                return {
                    "success": False,
                    "error": "No slides were successfully detailed"
                }

            logger.info(f"✅ Step 2 completed: {len(detailed_slides)} slides detailed")

            return {
                "success": True,
                "slides": detailed_slides,
                "total_processed": len(slide_framework),
                "successful_count": len(detailed_slides)
            }

        except Exception as e:
            logger.error(f"Error in step 2 - detailing slides: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _detail_single_slide(
        self,
        lesson_content: str,
        slide_info: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Chi tiết hóa một slide cụ thể với fallback logic

        Args:
            lesson_content: Nội dung bài học gốc
            slide_info: Thông tin slide từ framework
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            Dict chứa slide đã chi tiết hóa
        """
        try:
            slide_number = slide_info.get("slide_number", 1)
            slide_title = slide_info.get("title", "")
            slide_purpose = slide_info.get("purpose", "")
            slide_knowledge = slide_info.get("knowledge", "")

            # Tạo prompt cho bước 2
            default_prompt_2 = self._get_default_prompt_2()
            final_config = config_prompt if config_prompt else ""

            detail_prompt = f"""
{default_prompt_2}

{final_config}

NỘI DUNG BÀI HỌC GỐC:
{lesson_content}

THÔNG TIN SLIDE CẦN CHI TIẾT HÓA:
- Số thứ tự: Slide {slide_number}
- Tiêu đề: {slide_title}
- Mục đích: {slide_purpose}
- Kiến thức cần truyền đạt: {slide_knowledge}

HƯỚNG DẪN CHI TIẾT HÓA:
1. Dựa trên nội dung bài học gốc và thông tin slide
2. Tạo nội dung chi tiết, đầy đủ cho slide này
3. Điều chỉnh thái độ, cách nói phù hợp với đối tượng
4. Đảm bảo nội dung chính xác và có căn cứ từ bài học

YÊU CẦU OUTPUT:
Tạo nội dung chi tiết hoàn chỉnh cho slide {slide_number}, bao gồm tất cả thông tin cần thiết để truyền đạt kiến thức một cách hiệu quả.
"""

            # Retry logic: Thử tối đa 3 lần
            max_retries = 3
            for attempt in range(max_retries):
                logger.info(f"🤖 Detailing slide {slide_number}, attempt {attempt + 1}/{max_retries}")

                llm_result = await self.llm_service.generate_content(
                    prompt=detail_prompt,
                    temperature=0.1,
                    max_tokens=15000
                )

                if llm_result["success"] and llm_result.get("text") and llm_result["text"].strip():
                    detailed_content = llm_result["text"].strip()

                    # Validate content quality (basic check)
                    if len(detailed_content) > 50:  # Minimum content length
                        logger.info(f"✅ Slide {slide_number} detailed successfully on attempt {attempt + 1}")

                        return {
                            "success": True,
                            "slide": {
                                "slide_number": slide_number,
                                "title": slide_title,
                                "purpose": slide_purpose,
                                "knowledge": slide_knowledge,
                                "detailed_content": detailed_content,
                                "framework_info": slide_info
                            }
                        }
                    else:
                        logger.warning(f"⚠️ Slide {slide_number} content too short on attempt {attempt + 1}")
                else:
                    logger.warning(f"❌ LLM failed for slide {slide_number} on attempt {attempt + 1}: {llm_result.get('error')}")

                # Wait before retry
                if attempt < max_retries - 1:
                    import asyncio
                    await asyncio.sleep(1)

            # Fallback: Trả về content gốc nếu thất bại sau 3 lần
            logger.warning(f"⚠️ Failed to detail slide {slide_number} after {max_retries} attempts, using fallback")

            fallback_content = f"""
Tiêu đề: {slide_title}

Mục đích: {slide_purpose}

Nội dung: {slide_knowledge}

[Nội dung chi tiết sẽ được bổ sung dựa trên thông tin từ bài học gốc]
"""

            return {
                "success": True,
                "slide": {
                    "slide_number": slide_number,
                    "title": slide_title,
                    "purpose": slide_purpose,
                    "knowledge": slide_knowledge,
                    "detailed_content": fallback_content,
                    "framework_info": slide_info,
                    "is_fallback": True
                }
            }

        except Exception as e:
            logger.error(f"Error detailing single slide: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _step3_attach_placeholders(
        self,
        detailed_slides: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        BƯỚC 3: Gắn placeholder cho từng slide (hàm độc lập)

        Args:
            detailed_slides: Danh sách slides đã chi tiết hóa từ bước 2

        Returns:
            Dict chứa slides đã gắn placeholder
        """
        try:
            logger.info(f"🏷️ Step 3: Attaching placeholders to {len(detailed_slides)} slides...")

            if not detailed_slides:
                return {
                    "success": False,
                    "error": "Empty detailed slides provided for placeholder attachment"
                }

            final_slides = []

            # Xử lý từng slide để gắn placeholder
            for slide_data in detailed_slides:
                slide_number = slide_data.get("slide_number", 1)
                logger.info(f"🏷️ Attaching placeholders to slide {slide_number}")

                # Gắn placeholder cho slide này
                placeholder_result = await self._attach_placeholders_to_single_slide(slide_data)

                if placeholder_result["success"]:
                    final_slides.append(placeholder_result["slide"])
                    logger.info(f"✅ Placeholders attached to slide {slide_number}")
                else:
                    logger.warning(f"❌ Failed to attach placeholders to slide {slide_number}: {placeholder_result.get('error')}")
                    # Tiếp tục với slide tiếp theo
                    continue

            if not final_slides:
                return {
                    "success": False,
                    "error": "No slides successfully processed for placeholder attachment"
                }

            logger.info(f"✅ Step 3 completed: {len(final_slides)} slides with placeholders")

            return {
                "success": True,
                "slides": final_slides,
                "total_processed": len(detailed_slides),
                "successful_count": len(final_slides)
            }

        except Exception as e:
            logger.error(f"Error in step 3 - attaching placeholders: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _attach_placeholders_to_single_slide(
        self,
        slide_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Gắn placeholder cho một slide cụ thể

        Args:
            slide_data: Dữ liệu slide đã chi tiết hóa

        Returns:
            Dict chứa slide đã gắn placeholder
        """
        try:
            slide_number = slide_data.get("slide_number", 1)
            detailed_content = slide_data.get("detailed_content", "")

            if not detailed_content or not detailed_content.strip():
                return {
                    "success": False,
                    "error": f"Empty detailed content for slide {slide_number}"
                }

            # Tạo prompt cho bước 3
            default_prompt_3 = self._get_default_prompt_3()

            placeholder_prompt = f"""
{default_prompt_3}

NỘI DUNG SLIDE ĐÃ CHI TIẾT HÓA:
{detailed_content}

HƯỚNG DẪN GẮN PLACEHOLDER:
1. Phân tích nội dung và xác định loại thông tin
2. Gắn placeholder phù hợp cho từng phần nội dung
3. Sử dụng format: nội dung #*(PlaceholderType)*#
4. Không thay đổi ý nghĩa nội dung gốc

PLACEHOLDER TYPES AVAILABLE:
- LessonName: Tên bài học
- LessonDescription: Mô tả bài học
- CreatedDate: Ngày tạo
- TitleName: Tên tiêu đề chính
- TitleContent: Nội dung tiêu đề chính
- SubtitleName: Tên tiêu đề phụ
- SubtitleContent: Nội dung tiêu đề phụ
- ImageName: Tên hình ảnh
- ImageContent: Mô tả hình ảnh

YÊU CẦU OUTPUT:
Trả về nội dung slide đã gắn placeholder theo đúng quy tắc, không thay đổi ý nghĩa nội dung gốc.
"""

            # Gọi LLM để gắn placeholder
            logger.info(f"🤖 Calling LLM to attach placeholders for slide {slide_number}")

            llm_result = await self.llm_service.generate_content(
                prompt=placeholder_prompt,
                temperature=0.1,
                max_tokens=10000
            )

            if not llm_result["success"] or not llm_result.get("text"):
                return {
                    "success": False,
                    "error": f"Failed to attach placeholders: {llm_result.get('error', 'Empty response')}"
                }

            placeholder_content = llm_result["text"].strip()

            # Validate placeholder format (basic check)
            if "#*(" in placeholder_content and ")*#" in placeholder_content:
                logger.info(f"✅ Placeholders attached successfully for slide {slide_number}")

                # Tạo slide final với placeholder
                final_slide = slide_data.copy()
                final_slide["placeholder_content"] = placeholder_content
                final_slide["has_placeholders"] = True

                return {
                    "success": True,
                    "slide": final_slide
                }
            else:
                logger.warning(f"⚠️ No valid placeholders found in output for slide {slide_number}")

                # Fallback: Trả về content gốc
                final_slide = slide_data.copy()
                final_slide["placeholder_content"] = detailed_content
                final_slide["has_placeholders"] = False
                final_slide["is_fallback"] = True

                return {
                    "success": True,
                    "slide": final_slide
                }

        except Exception as e:
            logger.error(f"Error attaching placeholders to single slide: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _validate_input(self, lesson_id: str, template_id: str) -> Dict[str, Any]:
        """
        Validate input parameters

        Args:
            lesson_id: ID của bài học
            template_id: ID của template

        Returns:
            Dict chứa kết quả validation
        """
        errors = []

        if not lesson_id or not lesson_id.strip():
            errors.append("lesson_id is empty or invalid")

        if not template_id or not template_id.strip():
            errors.append("template_id is empty or invalid")

        if errors:
            return {
                "success": False,
                "errors": errors,
                "error": "; ".join(errors)
            }

        return {"success": True}

    def _log_workflow_summary(self, workflow_result: Dict[str, Any]) -> None:
        """
        Log tóm tắt kết quả workflow

        Args:
            workflow_result: Kết quả từ workflow
        """
        try:
            if workflow_result.get("success"):
                framework = workflow_result.get("framework", {})
                detailed = workflow_result.get("detailed", {})
                final = workflow_result.get("final", {})

                logger.info("📊 WORKFLOW SUMMARY:")
                logger.info(f"   Step 1 - Framework: {len(framework.get('slides', []))} slides created")
                logger.info(f"   Step 2 - Detailed: {detailed.get('successful_count', 0)}/{detailed.get('total_processed', 0)} slides detailed")
                logger.info(f"   Step 3 - Placeholders: {final.get('successful_count', 0)}/{final.get('total_processed', 0)} slides with placeholders")
                logger.info(f"   Final result: {len(workflow_result.get('slides', []))} slides ready")
            else:
                logger.error(f"❌ WORKFLOW FAILED: {workflow_result.get('error', 'Unknown error')}")

        except Exception as e:
            logger.warning(f"Error logging workflow summary: {e}")


# Singleton instance getter
def get_optimized_slide_generation_service() -> OptimizedSlideGenerationService:
    """
    Get singleton instance của OptimizedSlideGenerationService

    Returns:
        OptimizedSlideGenerationService instance
    """
    return OptimizedSlideGenerationService()
