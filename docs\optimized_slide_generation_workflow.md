# Optimized Slide Generation Workflow

## Tổng quan

Workflow tối ưu hóa quy trình tạo slide từ nội dung bài học (lesson_content) bằng LLM theo 3 bước riêng biệt:

1. **Bước 1: <PERSON><PERSON><PERSON> dựng khung slide** - Tạ<PERSON> khung tổng quát
2. **Bước 2: Chi tiết hóa từng slide** - Xử lý tuần tự từng slide
3. **Bước 3: Gắn placeholder** - Hàm độc lập gắn placeholder

## Kiến trúc Workflow

### 🔄 Flow điều khiển chính

```
Input: lesson_content, template_id, config_prompt
    ↓
[Validation] → Kiểm tra input rỗng/thiếu dữ liệu
    ↓
[Step 1] → Xây dựng khung slide (default_prompt_1 + config_prompt)
    ↓
[Step 2] → Chi tiết hóa tuần tự: Slide 1 → Slide 2 → Slide 3 → ...
    ↓
[Step 3] → Gắn placeholder cho từng slide chi tiết
    ↓
Output: Slides hoàn chỉnh với placeholder
```

### 📋 Bước 1: <PERSON><PERSON><PERSON> dựng khung slide

**Input:** 
- `lesson_content`: Nội dung bài học
- `default_prompt_1`: Prompt cho khung slide
- `config_prompt`: Prompt cấu hình tùy chỉnh

**Output:**
- Khung slide tổng quát với tiêu đề, mục đích, kiến thức cần truyền đạt

**Mục tiêu:**
- Tách lesson_content thành các slide với mục đích rõ ràng
- Đảm bảo khung slide có tính logic, hợp lý và dễ theo dõi
- Không cần nội dung chi tiết, chỉ cần cấu trúc tổng quát

### 📝 Bước 2: Chi tiết hóa từng slide

**Input:**
- `lesson_content`: Nội dung bài học gốc
- `default_prompt_2`: Prompt cho chi tiết hóa
- `config_prompt`: Prompt cấu hình
- `khung_slide`: Khung từ bước 1

**Hoạt động:**
- Chi tiết hóa nội dung cho từng slide cụ thể
- Điều chỉnh thái độ, cách nói phù hợp với đối tượng
- Xử lý tuần tự: Slide 1 → Slide 2 → Slide 3 → ...

**Fallback Logic:**
- Retry tối đa 3 lần cho mỗi slide
- Nếu thất bại → trả về content gốc từ khung slide

### 🏷️ Bước 3: Gắn placeholder

**Input:**
- `slide_chi_tiet`: Slides đã chi tiết hóa từ bước 2
- `default_prompt_3`: Prompt cho gắn placeholder

**Hoạt động:**
- Gắn placeholder theo quy tắc hiện tại
- Tách riêng thành hàm độc lập
- Không tự ý thay đổi quy tắc gắn

## Default Prompts

### 🎯 default_prompt_1 (Khung slide)
```
Bạn là chuyên gia phân tích nội dung giáo dục. 
Nhiệm vụ: Tạo KHUNG SLIDE tổng quát từ lesson_content.

NGUYÊN TẮC:
- PHÂN TÍCH TOÀN DIỆN
- CẤU TRÚC LOGIC  
- MỤC ĐÍCH RÕ RÀNG
- TÍNH LIÊN KẾT

OUTPUT: Khung slide với tiêu đề và mục đích từng slide
```

### ✍️ default_prompt_2 (Chi tiết hóa)
```
Bạn là chuyên gia viết nội dung thuyết trình giáo dục.
Nhiệm vụ: Chi tiết hóa nội dung cho một slide cụ thể.

NGUYÊN TẮC:
- NỘI DUNG ĐẦY ĐỦ
- PHONG CÁCH PHÙ HỢP
- TÍNH CHÍNH XÁC
- DỄ HIỂU

OUTPUT: Nội dung chi tiết cho slide được chỉ định
```

### 🏷️ default_prompt_3 (Gắn placeholder)
```
Bạn là chuyên gia xử lý định dạng nội dung slide.
Nhiệm vụ: Gắn placeholder cho nội dung đã chi tiết hóa.

QUY TẮC:
- CHÍNH XÁC
- NHẤT QUÁN  
- ĐẦY ĐỦ
- TUÂN THỦ

OUTPUT: Nội dung với placeholder format: nội dung #*(PlaceholderType)*#
```

## Xử lý lỗi và Fallback

### ❌ Xử lý lỗi input
- Bắt lỗi input rỗng hoặc thiếu dữ liệu quan trọng
- Trả về lỗi và dừng workflow tại đó
- Không xử lý tiếp nếu input không hợp lệ

### 🔄 Fallback Logic
- **Bước 2**: Retry 3 lần cho mỗi slide
  - Nếu thất bại → trả về content gốc từ khung
  - Tiếp tục với slide tiếp theo
- **Bước 3**: Nếu không gắn được placeholder
  - Trả về content chi tiết gốc
  - Đánh dấu `has_placeholders: false`

### 🧹 Cleanup Code
- Xóa các hàm thừa không phục vụ workflow mới
- Chỉ giữ lại các hàm cần thiết cho 3 bước
- Không implement fallback nếu không được yêu cầu

## API Endpoints

### POST `/api/v1/optimized-slides/generate`
Tạo slides sử dụng workflow tối ưu hóa 3 bước

### GET `/api/v1/optimized-slides/health`
Kiểm tra trạng thái service

### POST `/api/v1/optimized-slides/test-workflow`
Test từng bước workflow để debug

## Sử dụng

```python
from app.services.optimized_slide_generation_service import get_optimized_slide_generation_service

service = get_optimized_slide_generation_service()

result = await service.generate_slides_from_lesson_optimized(
    lesson_id="lesson_123",
    template_id="template_456", 
    config_prompt="Tạo slide phù hợp với học sinh lớp 10",
    presentation_title="Bài học Toán 10"
)
```

## Lợi ích

1. **Tách biệt rõ ràng**: Mỗi bước có trách nhiệm cụ thể
2. **Dễ debug**: Có thể test từng bước riêng biệt  
3. **Fallback tốt**: Xử lý lỗi không làm crash toàn bộ workflow
4. **Tối ưu prompt**: Mỗi bước có prompt chuyên biệt
5. **Linh hoạt**: Có thể tùy chỉnh từng bước độc lập

## Monitoring và Logging

- Logger.info cho mỗi bước quan trọng
- Tracking số lượng slides thành công/thất bại
- Summary report cuối workflow
- Error handling chi tiết cho từng bước
